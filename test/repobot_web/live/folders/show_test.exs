defmodule RepobotWeb.Live.Folders.ShowTest do
  use RepobotWeb.ConnCase, async: false
  use Oban.Testing, repo: Repobot.Repo

  import Phoenix.LiveViewTest
  import Repobot.Test.Fixtures
  import Mox

  setup :set_mox_from_context
  setup :verify_on_exit!

  # Helper function to switch tabs in tests
  defp switch_to_tab(view, tab_name) do
    view
    |> element("button[phx-click='switch_tab'][phx-value-tab='#{tab_name}']")
    |> render_click()
  end

  describe "mount with files" do
    setup do
      user = create_user()

      # Create a folder
      folder =
        create_folder(%{
          name: "Test Folder",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create two repositories with similar files
      repo1 =
        create_repository(%{
          name: "repo1",
          owner: "owner1",
          full_name: "owner1/repo1",
          user_id: user.id,
          organization_id: user.default_organization_id,
          folder_id: folder.id,
          settings: %{"provider" => "github"}
        })

      repo2 =
        create_repository(%{
          name: "repo2",
          owner: "owner1",
          full_name: "owner1/repo2",
          user_id: user.id,
          organization_id: user.default_organization_id,
          folder_id: folder.id,
          settings: %{"provider" => "github"}
        })

      # Add similar files to both repositories
      create_repository_file(%{
        repository_id: repo1.id,
        path: "lib/test.ex",
        name: "test.ex",
        type: "file",
        size: 100,
        sha: "abc123",
        content:
          "defmodule Test do\n  @moduledoc \"Test module\"\n  def hello, do: :world\nend\n",
        content_updated_at: DateTime.utc_now()
      })

      create_repository_file(%{
        repository_id: repo2.id,
        path: "lib/test.ex",
        name: "test.ex",
        type: "file",
        size: 100,
        sha: "def456",
        content:
          "defmodule Test do\n  @moduledoc \"Test module\"\n  def hello, do: :earth\nend\n",
        content_updated_at: DateTime.utc_now()
      })

      # Preload files with content for both repositories
      repo1 = Repobot.Repositories.preload_files_with_content(repo1)
      repo2 = Repobot.Repositories.preload_files_with_content(repo2)

      # Preload repositories for the folder
      folder = %{folder | repositories: [repo1, repo2]}

      # Stub GitHub client for all tests
      test_client = %Tentacat.Client{auth: %{access_token: "test_token"}}
      Repobot.Test.GitHubMock |> stub(:client, fn _user -> test_client end)

      # Stub GitHub API methods that might be called during repository refresh
      Repobot.Test.GitHubMock
      |> stub(:get_tree, fn _client, _owner, _repo ->
        {:ok,
         [
           %{"path" => "lib/test.ex", "type" => "file", "size" => 100, "sha" => "abc123"},
           %{"path" => "README.md", "type" => "file", "size" => 50, "sha" => "def456"}
         ]}
      end)

      {:ok, user: user, folder: folder, repo1: repo1, repo2: repo2}
    end

    test "does not refresh repositories that already have files", %{
      conn: conn,
      user: user,
      folder: folder,
      repo1: repo1,
      repo2: repo2
    } do
      # We should not expect any GitHub API calls since both repos have files

      {:ok, view, html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/folders/#{folder}")

      # Initial render should show repository names
      assert html =~ repo2.full_name
      assert html =~ repo1.full_name

      # Switch to common files tab to trigger CommonFiles component loading
      switch_to_tab(view, "common")

      # Wait for common files analysis to complete
      assert_eventually(
        fn ->
          html = render(view)

          refute html =~ "Refreshing..."
          assert html =~ ~r/test\.ex/
          assert html =~ "75% similar"
        end,
        2000
      )
    end

    test "shows common files section after all files are loaded", %{
      conn: conn,
      user: user,
      folder: folder
    } do
      # This test uses the setup that already creates files with content,
      # so no GitHub API calls should be needed

      {:ok, view, _html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/folders/#{folder}")

      # Switch to common files tab to trigger CommonFiles component loading
      switch_to_tab(view, "common")

      # Wait for the loading to complete and verify common files section
      assert_eventually(fn ->
        html = render(view)
        assert html =~ "Common Files"
        assert html =~ ~r/test\.ex/
        assert html =~ "% similar"
        refute html =~ "Refreshing..."
      end)
    end
  end

  describe "mount with refresh" do
    setup do
      user = create_user()

      # Create a folder
      folder =
        create_folder(%{
          name: "Test Folder",
          organization_id: user.default_organization_id
        })

      # Create two repositories without files so we can find common files
      repo1 =
        create_repository(%{
          name: "repo1",
          owner: "owner1",
          full_name: "owner1/repo1",
          user_id: user.id,
          organization_id: user.default_organization_id,
          folder_id: folder.id,
          settings: %{"provider" => "github"}
        })

      repo2 =
        create_repository(%{
          name: "repo2",
          owner: "owner1",
          full_name: "owner1/repo2",
          user_id: user.id,
          organization_id: user.default_organization_id,
          folder_id: folder.id,
          settings: %{"provider" => "github"}
        })

      # Preload files for the repositories (will be empty)
      repo1 = Repobot.Repo.preload(repo1, :files)
      repo2 = Repobot.Repo.preload(repo2, :files)

      # Preload repositories for the folder
      folder = %{folder | repositories: [repo1, repo2]}

      # Create test client for GitHub API mocks
      test_client = %Tentacat.Client{auth: %{access_token: "test_token"}}

      # Stub GitHub client for all tests
      Repobot.Test.GitHubMock |> stub(:client, fn _user -> test_client end)

      {:ok, user: user, folder: folder, repo1: repo1, repo2: repo2, test_client: test_client}
    end

    test "loads repository trees using RepositoryFilesWorker when repositories have no files", %{
      conn: conn,
      user: user,
      folder: folder,
      repo1: repo1,
      repo2: repo2
    } do
      # Verify repositories have no files initially
      assert Enum.empty?(repo1.files)
      assert Enum.empty?(repo2.files)

      # Mock GitHub API calls for tree loading for both repositories
      # Note: The worker may or may not be triggered depending on the exact timing
      # and conditions in the test environment, so we use stub to allow the calls
      # but don't require a specific count
      Repobot.Test.GitHubMock
      |> stub(:get_tree, fn _client, "owner1", _repo_name ->
        {:ok,
         [
           %{"path" => "lib/common.ex", "type" => "file", "size" => 100, "sha" => "abc123"},
           %{"path" => "README.md", "type" => "file", "size" => 50, "sha" => "def456"}
         ]}
      end)
      |> stub(:get_file_content, fn _client, "owner1", repo_name, file_path ->
        case file_path do
          "lib/common.ex" ->
            {:ok, "defmodule Common do\n  def test, do: :ok\nend\n", %{}}

          "README.md" ->
            {:ok, "# #{repo_name}\n\nA test repository.\n", %{}}
        end
      end)

      {:ok, view, html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/folders/#{folder}")

      # Initial render should show repository names
      assert html =~ repo1.full_name
      assert html =~ repo2.full_name

      # Switch to common files tab to trigger CommonFiles component loading
      switch_to_tab(view, "common")

      # Should show loading indicator initially
      assert_eventually(fn ->
        html = render(view)
        html =~ "Finding common files..."
      end)

      # Manually trigger the trees_loaded event to simulate Oban job completion
      # This is needed because while Oban jobs run inline in tests (testing: :inline),
      # the Oban.Notifier system doesn't work reliably in the test environment,
      # so the completion notification doesn't reach the LiveView component
      send(view.pid, {:repositories_event, "trees_loaded", %{}})

      # Wait for the initial loading to complete
      # The system should handle repositories with no files gracefully
      assert_eventually(fn ->
        html = render(view)
        # Loading should complete without crashing
        refute html =~ "Finding common files..."
        # The system should either show "No common files found" or successfully load files
        html =~ "No common files found" or html =~ "lib/common.ex"
      end)
    end

    test "refreshes file content using RepositoryFilesWorker when files lack content", %{
      conn: conn,
      user: user,
      folder: folder,
      repo1: repo1,
      repo2: repo2
    } do
      # Create the same file in both repositories to ensure it's a "common file"
      # Only repo1 has nil content (simulating tree-only load)
      create_repository_file(%{
        repository_id: repo1.id,
        path: "lib/common.ex",
        name: "common.ex",
        type: "file",
        size: 100,
        sha: "abc123",
        # No content initially
        content: nil,
        content_updated_at: nil
      })

      # Create the same file in repo2 with content so it becomes a common file
      create_repository_file(%{
        repository_id: repo2.id,
        path: "lib/common.ex",
        name: "common.ex",
        type: "file",
        size: 100,
        sha: "def456",
        content: "defmodule Common do\n  def existing, do: :ok\nend\n",
        content_updated_at: DateTime.utc_now()
      })

      # Reload repositories with files
      repo1 = Repobot.Repo.preload(repo1, :files, force: true)
      repo2 = Repobot.Repo.preload(repo2, :files, force: true)

      # Mock GitHub API calls for content refresh - both files will be refreshed
      Repobot.Test.GitHubMock
      |> stub(:get_file_content, fn _client, "owner1", repo_name, "lib/common.ex" ->
        case repo_name do
          "repo1" -> {:ok, "defmodule Common do\n  def test, do: :ok\nend\n", %{}}
          "repo2" -> {:ok, "defmodule Common do\n  def existing, do: :ok\nend\n", %{}}
        end
      end)

      {:ok, view, html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/folders/#{folder}")

      # Initial render should show both repository names
      assert html =~ repo1.full_name
      assert html =~ repo2.full_name

      # Switch to common files tab to trigger CommonFiles component loading
      switch_to_tab(view, "common")

      # Wait for content refresh to complete
      assert_eventually(fn ->
        html = render(view)

        # Loading should be complete
        refute html =~ "Refreshing file content..."
        refute html =~ "Finding common files..."
      end)

      # Verify file content was refreshed
      updated_file =
        Repobot.Repositories.get_repository!(repo1.id)
        |> Map.get(:files)
        |> Enum.find(&(&1.path == "lib/common.ex"))

      assert updated_file.content == "defmodule Common do\n  def test, do: :ok\nend\n"
      refute is_nil(updated_file.content_updated_at)
    end

    test "handles RepositoryFilesWorker errors gracefully", %{
      conn: conn,
      user: user,
      folder: folder,
      repo1: repo1,
      repo2: repo2
    } do
      # Remove repo2 from the folder in the database so only repo1 is processed
      Repobot.Repo.get!(Repobot.Repository, repo2.id)
      |> Ecto.Changeset.change(folder_id: nil)
      |> Repobot.Repo.update!()

      # Mock GitHub API to return an error for the worker
      Repobot.Test.GitHubMock
      |> expect(:get_tree, fn _client, "owner1", "repo1" ->
        {:error, "API rate limit exceeded"}
      end)

      {:ok, view, html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/folders/#{folder}")

      # Initial render should show repository name
      assert html =~ repo1.full_name

      # Wait for the worker to process and handle the error
      assert_eventually(
        fn ->
          html = render(view)
          # Should show error message somewhere in the view
          html =~ "Failed to load repository files" or html =~ "API rate limit exceeded"
        end,
        5000
      )
    end

    test "manual refresh uses RepositoryFilesWorker for content refresh", %{
      conn: conn,
      user: user,
      folder: folder,
      repo1: repo1,
      repo2: repo2
    } do
      # Create the same file in both repositories to make it a common file
      # repo1 has content, repo2 has nil content to trigger refresh
      create_repository_file(%{
        repository_id: repo1.id,
        path: "lib/common.ex",
        name: "common.ex",
        type: "file",
        size: 100,
        sha: "abc123",
        content: "defmodule Common do\n  def old_test, do: :old\nend\n",
        content_updated_at: DateTime.utc_now()
      })

      create_repository_file(%{
        repository_id: repo2.id,
        path: "lib/common.ex",
        name: "common.ex",
        type: "file",
        size: 100,
        sha: "abc123",
        content: nil,
        content_updated_at: nil
      })

      # Reload repositories with files
      repo1 = Repobot.Repo.preload(repo1, :files, force: true)
      repo2 = Repobot.Repo.preload(repo2, :files, force: true)

      # Mock GitHub API calls for content refresh with updated content
      # During manual refresh, all files are refreshed regardless of existing content
      Repobot.Test.GitHubMock
      |> stub(:client, fn _user -> %Tentacat.Client{auth: %{access_token: "test_token"}} end)
      |> stub(:get_file_content, fn _client, "owner1", repo_name, "lib/common.ex" ->
        case repo_name do
          "repo1" -> {:ok, "defmodule Common do\n  def new_test, do: :new\nend\n", %{}}
          "repo2" -> {:ok, "defmodule Common do\n  def new_test, do: :new\nend\n", %{}}
        end
      end)

      {:ok, view, _html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/folders/#{folder}")

      # Switch to common files tab to trigger CommonFiles component loading
      switch_to_tab(view, "common")

      # Wait for initial load to complete
      assert_eventually(
        fn ->
          html = render(view)
          refute html =~ "Finding common files..."
        end,
        3000
      )

      # Click the manual refresh button in the CommonFiles component
      view
      |> element("button", "Refresh")
      |> render_click()

      # Should show refreshing indicator
      assert_eventually(
        fn ->
          html = render(view)
          html =~ "Refreshing..." or html =~ "Refreshing file content..."
        end,
        2000
      )

      # Wait for refresh to complete
      assert_eventually(
        fn ->
          html = render(view)
          refute html =~ "Refreshing file content..."
          refute html =~ "Refreshing..."
        end,
        5000
      )

      # Verify file content was updated in both repositories
      updated_file_repo1 =
        Repobot.Repositories.get_repository!(repo1.id)
        |> Map.get(:files)
        |> Enum.find(&(&1.path == "lib/common.ex"))

      updated_file_repo2 =
        Repobot.Repositories.get_repository!(repo2.id)
        |> Map.get(:files)
        |> Enum.find(&(&1.path == "lib/common.ex"))

      assert updated_file_repo1.content == "defmodule Common do\n  def new_test, do: :new\nend\n"
      assert updated_file_repo2.content == "defmodule Common do\n  def new_test, do: :new\nend\n"
    end
  end

  describe "webhook integration" do
    setup do
      user = create_user()

      # Create a folder
      folder =
        create_folder(%{
          name: "Test Folder",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create two repositories with files
      repo1 =
        create_repository(%{
          name: "repo1",
          owner: "owner1",
          full_name: "owner1/repo1",
          user_id: user.id,
          organization_id: user.default_organization_id,
          folder_id: folder.id,
          settings: %{"provider" => "github"}
        })

      repo2 =
        create_repository(%{
          name: "repo2",
          owner: "owner1",
          full_name: "owner1/repo2",
          user_id: user.id,
          organization_id: user.default_organization_id,
          folder_id: folder.id,
          settings: %{"provider" => "github"}
        })

      # Add similar files to both repositories
      create_repository_file(%{
        repository_id: repo1.id,
        path: "lib/common.ex",
        name: "common.ex",
        type: "file",
        size: 100,
        sha: "abc123",
        content: "defmodule Common do\n  def old_version, do: :old\nend\n",
        content_updated_at: DateTime.utc_now()
      })

      create_repository_file(%{
        repository_id: repo2.id,
        path: "lib/common.ex",
        name: "common.ex",
        type: "file",
        size: 100,
        sha: "def456",
        content: "defmodule Common do\n  def old_version, do: :old\nend\n",
        content_updated_at: DateTime.utc_now()
      })

      # Preload files for both repositories
      repo1 = Repobot.Repo.preload(repo1, :files)
      repo2 = Repobot.Repo.preload(repo2, :files)

      # Preload repositories for the folder
      folder = %{folder | repositories: [repo1, repo2]}

      # Stub GitHub client for all tests
      test_client = %Tentacat.Client{auth: %{access_token: "test_token"}}
      Repobot.Test.GitHubMock |> stub(:client, fn _user -> test_client end)

      {:ok, user: user, folder: folder, repo1: repo1, repo2: repo2}
    end

    test "automatically refreshes common files when repository is updated via webhook", %{
      conn: conn,
      user: user,
      folder: folder,
      repo1: repo1,
      repo2: repo2
    } do
      # Mock GitHub API calls for content refresh with updated content
      # The webhook should trigger a content refresh, so we need to mock the API calls
      Repobot.Test.GitHubMock
      |> stub(:get_file_content, fn _client, "owner1", repo_name, "lib/common.ex" ->
        case repo_name do
          "repo1" -> {:ok, "defmodule Common do\n  def new_version, do: :new\nend\n", %{}}
          "repo2" -> {:ok, "defmodule Common do\n  def old_version, do: :old\nend\n", %{}}
        end
      end)

      {:ok, view, html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/folders/#{folder}")

      # Initial render should show both repository names
      assert html =~ repo1.full_name
      assert html =~ repo2.full_name

      # Switch to common files tab to trigger CommonFiles component loading
      switch_to_tab(view, "common")

      # Wait for initial common files analysis to complete
      assert_eventually(
        fn ->
          html = render(view)
          refute html =~ "Finding common files..."
          refute html =~ "Refreshing..."
          html =~ "lib/common.ex"
        end,
        5000
      )

      # Simulate a repository files updated event from a webhook
      # This mimics what happens when GitHub sends a push webhook
      send(
        view.pid,
        {:repository_files_updated, repo1.id,
         %{
           changed_files_count: 1,
           processed_files_count: 1,
           trigger: "push_webhook"
         }}
      )

      # Give the message a moment to be processed
      Process.sleep(100)

      # Should show the flash message about automatic update
      assert_eventually(
        fn ->
          html = render(view)
          assert html =~ "Repository files updated automatically"
        end,
        3000
      )

      assert_eventually(fn ->
        html = render(view)
        refute html =~ "Refreshing file content..."
        refute html =~ "Refreshing..."
      end)

      # The webhook event should trigger recalculation but not necessarily content refresh
      # The file content should remain the same unless explicitly refreshed
      updated_file_repo1 =
        Repobot.Repositories.get_repository!(repo1.id)
        |> Map.get(:files)
        |> Enum.find(&(&1.path == "lib/common.ex"))

      # Content should remain the same as it was initially set
      assert updated_file_repo1.content ==
               "defmodule Common do\n  def old_version, do: :old\nend\n"
    end

    test "ignores repository updates for repositories not in the folder", %{
      conn: conn,
      user: user,
      folder: folder,
      repo1: repo1,
      repo2: repo2
    } do
      # Create another repository not in this folder
      other_repo =
        create_repository(%{
          name: "other-repo",
          owner: "owner1",
          full_name: "owner1/other-repo",
          user_id: user.id,
          organization_id: user.default_organization_id,
          # Not in any folder
          folder_id: nil,
          settings: %{"provider" => "github"}
        })

      {:ok, view, html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/folders/#{folder}")

      # Initial render should show both repository names
      assert html =~ repo1.full_name
      assert html =~ repo2.full_name

      # Switch to common files tab to trigger CommonFiles component loading
      switch_to_tab(view, "common")

      # Wait for initial common files analysis to complete
      assert_eventually(
        fn ->
          html = render(view)
          refute html =~ "Finding common files..."
          refute html =~ "Refreshing..."
        end,
        5000
      )

      # Simulate a repository files updated event for a repository NOT in this folder
      send(
        view.pid,
        {:repository_files_updated, other_repo.id,
         %{
           changed_files_count: 1,
           processed_files_count: 1,
           trigger: "push_webhook"
         }}
      )

      # Give it a moment to process
      Process.sleep(100)

      # Should NOT show refreshing indicator or flash message
      html = render(view)
      refute html =~ "Refreshing..."
      refute html =~ "Repository files updated automatically"
    end
  end

  describe "generate_template" do
    setup do
      user = create_user()

      # Create a folder
      folder =
        create_folder(%{
          name: "Test Folder",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create two repositories with similar files
      repo1 =
        create_repository(%{
          name: "repo1",
          owner: "owner1",
          full_name: "owner1/repo1",
          user_id: user.id,
          organization_id: user.default_organization_id,
          folder_id: folder.id,
          settings: %{"provider" => "github"}
        })

      repo2 =
        create_repository(%{
          name: "repo2",
          owner: "owner1",
          full_name: "owner1/repo2",
          user_id: user.id,
          organization_id: user.default_organization_id,
          folder_id: folder.id,
          settings: %{"provider" => "github"}
        })

      # Add similar files to both repositories
      create_repository_file(%{
        repository_id: repo1.id,
        path: "lib/test.ex",
        name: "test.ex",
        type: "file",
        size: 100,
        sha: "abc123",
        content:
          "defmodule Test do\n  @moduledoc \"Test module\"\n  def hello, do: :world\nend\n",
        content_updated_at: DateTime.utc_now()
      })

      create_repository_file(%{
        repository_id: repo2.id,
        path: "lib/test.ex",
        name: "test.ex",
        type: "file",
        size: 100,
        sha: "def456",
        content:
          "defmodule Test do\n  @moduledoc \"Test module\"\n  def hello, do: :earth\nend\n",
        content_updated_at: DateTime.utc_now()
      })

      # Preload files for both repositories
      repo1 = Repobot.Repo.preload(repo1, :files)
      repo2 = Repobot.Repo.preload(repo2, :files)

      # Preload repositories for the folder
      folder = %{folder | repositories: [repo1, repo2]}

      # Stub GitHub client for all tests
      test_client = %Tentacat.Client{auth: %{access_token: "test_token"}}
      Repobot.Test.GitHubMock |> stub(:client, fn _user -> test_client end)

      # Stub GitHub API methods that might be called during repository refresh
      Repobot.Test.GitHubMock
      |> stub(:get_tree, fn _client, _owner, _repo ->
        {:ok,
         [
           %{"path" => "lib/test.ex", "type" => "file", "size" => 100, "sha" => "abc123"},
           %{"path" => "README.md", "type" => "file", "size" => 50, "sha" => "def456"}
         ]}
      end)

      {:ok, user: user, folder: folder, repo1: repo1, repo2: repo2}
    end

    test "successfully generates a template from two similar files", %{
      conn: conn,
      user: user,
      folder: folder
    } do
      # Mock GitHub API calls for content refresh
      Repobot.Test.GitHubMock
      |> stub(:get_file_content, fn _client, "owner1", _repo_name, "lib/test.ex" ->
        {:ok, "defmodule Test do\n  def test, do: :ok\nend\n", %{}}
      end)

      # Mock the AI backend response
      Repobot.Test.AIMock
      |> expect(:generate_template, fn file1, file2, path, vars, org ->
        assert String.contains?(file1, "defmodule Test")
        assert String.contains?(file2, "defmodule Test")
        assert path == "lib/test.ex"
        assert is_map(vars)
        assert org.id == user.default_organization_id
        {:ok, "defmodule {{ name }} do\n  def hello, do: {{ greeting }}\nend\n"}
      end)
      |> expect(:infer_tags, fn _source_file, org ->
        assert org.id == user.default_organization_id
        {:ok, ["elixir", "module", "template"]}
      end)

      {:ok, view, _html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/folders/#{folder}")

      # Switch to common files tab to trigger CommonFiles component loading
      switch_to_tab(view, "common")

      # Wait for common files analysis to complete and verify the button appears
      assert_eventually(fn ->
        refute render(view) =~ "Refreshing..."
        render(view) =~ ~s(data-button="generate-template")
      end)

      # Generate template
      assert view
             |> element("button[data-button='generate-template'][data-file-path='lib/test.ex']")
             |> render_click()

      # Verify success message
      assert_eventually(fn ->
        assert render(view) =~
                 "Successfully generated template from owner1/repo1 and owner1/repo2"
      end)
    end

    test "handles template generation errors", %{conn: conn, user: user, folder: folder} do
      Repobot.Test.AIMock
      |> expect(:generate_template, fn _file1, _file2, _path, _vars, _org ->
        {:error, "Failed to generate template"}
      end)

      {:ok, view, _html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/folders/#{folder}")

      # Switch to common files tab to trigger CommonFiles component loading
      switch_to_tab(view, "common")

      # Wait for common files analysis to complete and verify the button appears
      assert_eventually(fn ->
        refute render(view) =~ "Refreshing..."
        render(view) =~ ~s(data-button="generate-template")
      end)

      # Try to generate template
      assert view
             |> element("button[data-button='generate-template'][data-file-path='lib/test.ex']")
             |> render_click()

      # Verify error message
      assert_eventually(fn ->
        assert render(view) =~ "Failed to generate template: Failed to generate template"
      end)
    end
  end

  describe "read-only source files" do
    setup do
      user = create_user()

      # Create a folder
      folder =
        create_folder(%{
          name: "Test Folder",
          organization_id: user.default_organization_id
        })

      # Create a template repository
      template_repo =
        create_repository(%{
          name: "template-repo",
          owner: "owner1",
          full_name: "owner1/template-repo",
          user_id: user.id,
          organization_id: user.default_organization_id,
          template: true,
          settings: %{"provider" => "github"}
        })

      # Add template repository to folder
      {:ok, template_repo} = Repobot.Repositories.add_template_folder(template_repo, folder)

      # Create a read-only source file (imported from template repository)
      read_only_source_file =
        create_source_file(%{
          name: "readonly_test.ex",
          content: "test content",
          target_path: "lib/readonly_test.ex",
          user_id: user.id,
          organization_id: user.default_organization_id,
          source_repository_id: template_repo.id,
          read_only: true
        })

      # Create a regular editable source file
      editable_source_file =
        create_source_file(%{
          name: "editable_test.ex",
          content: "test content",
          target_path: "lib/editable_test.ex",
          user_id: user.id,
          organization_id: user.default_organization_id,
          read_only: false
        })

      # Associate both source files with the folder
      add_source_file_to_folder(read_only_source_file, folder)
      add_source_file_to_folder(editable_source_file, folder)

      # Stub GitHub client for all tests
      test_client = %Tentacat.Client{auth: %{access_token: "test_token"}}
      Repobot.Test.GitHubMock |> stub(:client, fn _user -> test_client end)

      # Stub GitHub API methods that might be called during repository refresh
      Repobot.Test.GitHubMock
      |> stub(:get_tree, fn _client, _owner, _repo ->
        {:ok,
         [
           %{"path" => "lib/test.ex", "type" => "file", "size" => 100, "sha" => "abc123"},
           %{"path" => "README.md", "type" => "file", "size" => 50, "sha" => "def456"}
         ]}
      end)

      {:ok,
       user: user,
       folder: folder,
       read_only_source_file: read_only_source_file,
       editable_source_file: editable_source_file,
       template_repo: template_repo}
    end

    test "does not show edit button for read-only source files", %{
      conn: conn,
      user: user,
      folder: folder,
      read_only_source_file: read_only_source_file,
      editable_source_file: editable_source_file
    } do
      {:ok, view, html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/folders/#{folder}")

      # Verify both source files are displayed
      assert html =~ read_only_source_file.name
      assert html =~ editable_source_file.name

      # Verify edit button is NOT present for read-only source file
      refute has_element?(
               view,
               "button[phx-value-source_file_id=\"#{read_only_source_file.id}\"]",
               "Edit"
             )

      # Verify edit button IS present for editable source file
      assert has_element?(
               view,
               "button[phx-value-source_file_id=\"#{editable_source_file.id}\"]",
               "Edit"
             )
    end
  end

  describe "move source files" do
    setup do
      user = create_user()

      # Create a folder
      folder =
        create_folder(%{
          name: "Test Folder",
          organization_id: user.default_organization_id
        })

      # Create a global source file
      source_file =
        create_source_file(%{
          name: "global_test.ex",
          content: "test content",
          target_path: "lib/global_test.ex",
          user_id: user.id,
          organization_id: user.default_organization_id,
          folder_id: nil
        })

      # Stub GitHub client for all tests
      test_client = %Tentacat.Client{auth: %{access_token: "test_token"}}
      Repobot.Test.GitHubMock |> stub(:client, fn _user -> test_client end)

      # Stub GitHub API methods that might be called during repository refresh
      Repobot.Test.GitHubMock
      |> stub(:get_tree, fn _client, _owner, _repo ->
        {:ok,
         [
           %{"path" => "lib/test.ex", "type" => "file", "size" => 100, "sha" => "abc123"},
           %{"path" => "README.md", "type" => "file", "size" => 50, "sha" => "def456"}
         ]}
      end)

      {:ok, user: user, folder: folder, source_file: source_file}
    end

    test "moves global source file to folder", %{
      conn: conn,
      user: user,
      folder: folder,
      source_file: source_file
    } do
      {:ok, view, _html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/folders/#{folder}")

      # Switch to global source files tab to see the move button
      switch_to_tab(view, "global")

      # Click the move to folder button
      assert view
             |> element("button", "Move to folder")
             |> render_click()

      # Verify success message
      assert render(view) =~ "Source file moved to folder"

      # Verify the source file is now in the folder
      updated_source_file = Repobot.SourceFiles.get_source_file!(source_file.id)
      updated_source_file = Repobot.Repo.preload(updated_source_file, :folders)
      folder_ids = Enum.map(updated_source_file.folders, & &1.id)
      assert folder.id in folder_ids
    end
  end

  describe "folder actions" do
    setup do
      user = create_user()

      # Create a folder
      folder =
        create_folder(%{
          name: "Test Folder",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create a repository in the folder
      repo =
        create_repository(%{
          name: "repo1",
          owner: "owner1",
          full_name: "owner1/repo1",
          user_id: user.id,
          organization_id: user.default_organization_id,
          folder_id: folder.id,
          settings: %{"provider" => "github"}
        })

      # Preload repositories for the folder
      folder = Repobot.Repo.preload(folder, :repositories)

      # Stub GitHub client for all tests
      test_client = %Tentacat.Client{auth: %{access_token: "test_token"}}
      Repobot.Test.GitHubMock |> stub(:client, fn _user -> test_client end)

      # Stub GitHub API methods that might be called during repository refresh
      Repobot.Test.GitHubMock
      |> stub(:get_tree, fn _client, _owner, _repo ->
        {:ok,
         [
           %{"path" => "lib/test.ex", "type" => "file", "size" => 100, "sha" => "abc123"},
           %{"path" => "README.md", "type" => "file", "size" => 50, "sha" => "def456"}
         ]}
      end)

      {:ok, user: user, folder: folder, repo: repo}
    end

    test "deletes folder and redirects to repositories", %{
      conn: conn,
      user: user,
      folder: folder,
      repo: repo
    } do
      {:ok, view, _html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/folders/#{folder}")

      # Click delete button and verify redirect
      assert view |> element("button", "Delete") |> render_click()
      assert_redirect(view, ~p"/repositories")

      # Verify folder is deleted
      assert_raise Ecto.NoResultsError, fn -> Repobot.Folders.get_folder!(folder.id) end

      # Verify repository's folder_id is now nil
      updated_repo = Repobot.Repositories.get_repository!(repo.id)
      assert updated_repo.folder_id == nil
    end

    test "toggles folder star status", %{conn: conn, user: user, folder: folder} do
      {:ok, view, _html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/folders/#{folder}")

      # Initial state should be unstarred
      assert has_element?(view, "button[aria-label=\"Star folder\"]")
      refute has_element?(view, "button[aria-label=\"Unstar folder\"]")

      # Click star button
      assert view |> element("button[aria-label=\"Star folder\"]") |> render_click()
      assert has_element?(view, "button[aria-label=\"Unstar folder\"]")

      # Click again to unstar
      assert view |> element("button[aria-label=\"Unstar folder\"]") |> render_click()
      assert has_element?(view, "button[aria-label=\"Star folder\"]")
    end
  end

  describe "import functionality" do
    setup do
      user = create_user()

      # Create a folder
      folder =
        create_folder(%{
          name: "Test Folder",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create a regular repository
      regular_repo =
        create_repository(%{
          name: "regular-repo",
          owner: "owner1",
          full_name: "owner1/regular-repo",
          user_id: user.id,
          organization_id: user.default_organization_id,
          folder_id: folder.id,
          template: false,
          settings: %{"provider" => "github"}
        })

      # Create a template repository (initially as regular, then convert to template)
      template_repo =
        create_repository(%{
          name: "template-repo",
          owner: "owner1",
          full_name: "owner1/template-repo",
          user_id: user.id,
          organization_id: user.default_organization_id,
          folder_id: folder.id,
          template: false,
          settings: %{"provider" => "github"}
        })

      # Convert to template and add to folder's template_repositories
      {:ok, template_repo} =
        Repobot.Repositories.update_repository(template_repo, %{template: true})

      {:ok, template_repo} = Repobot.Repositories.add_template_folder(template_repo, folder)

      # Create files for both repositories
      create_repository_file(%{
        repository_id: regular_repo.id,
        path: "README.md",
        name: "README.md",
        type: "file",
        size: 100,
        sha: "abc123",
        content: "Regular repo README",
        content_updated_at: DateTime.utc_now()
      })

      create_repository_file(%{
        repository_id: template_repo.id,
        path: "README.md",
        name: "README.md",
        type: "file",
        size: 100,
        sha: "def456",
        content: "Template repo README",
        content_updated_at: DateTime.utc_now()
      })

      # Preload files for both repositories
      regular_repo = Repobot.Repo.preload(regular_repo, :files)
      template_repo = Repobot.Repo.preload(template_repo, :files)

      # Reload the repositories to get the updated template status
      regular_repo = Repobot.Repositories.get_repository!(regular_repo.id)
      template_repo = Repobot.Repositories.get_repository!(template_repo.id)

      # Reload the folder with proper associations
      folder = Repobot.Folders.get_folder!(folder.id)

      # Stub GitHub client for all tests
      test_client = %Tentacat.Client{auth: %{access_token: "test_token"}}
      Repobot.Test.GitHubMock |> stub(:client, fn _user -> test_client end)

      {:ok, user: user, folder: folder, regular_repo: regular_repo, template_repo: template_repo}
    end

    test "import dropdown shows all repositories that have the file", %{
      conn: conn,
      user: user,
      folder: folder,
      regular_repo: regular_repo,
      template_repo: template_repo
    } do
      {:ok, view, _html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/folders/#{folder}")

      # Switch to common files tab to trigger CommonFiles component loading
      switch_to_tab(view, "common")

      # Wait for the common files to be calculated and displayed
      assert_eventually(fn ->
        html = render(view)
        refute html =~ "Finding common files..."
        # The common files should be available now that the tab is active
        true
      end)

      # Test the import functionality by directly calling the event on the CommonFiles component
      # This simulates clicking the "Import from..." button for README.md
      view
      |> element("[phx-click='show_import_dropdown'][phx-value-path='README.md']")
      |> render_click()

      # Verify that both repositories appear in the dropdown if they have the file
      # Check specifically for the dropdown content, not just anywhere in the HTML
      assert_eventually(fn ->
        html = render(view)
        html =~ template_repo.full_name and html =~ regular_repo.full_name
      end)
    end
  end

  describe "source files grouping by repository" do
    setup do
      user = create_user()

      # Create a folder
      folder =
        create_folder(%{
          name: "Test Folder",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create a template repository
      template_repo =
        create_repository(%{
          name: "template-repo",
          owner: "owner1",
          full_name: "owner1/template-repo",
          user_id: user.id,
          organization_id: user.default_organization_id,
          template: true,
          settings: %{"provider" => "github"}
        })

      # Add template repository to folder
      {:ok, template_repo} = Repobot.Repositories.add_template_folder(template_repo, folder)

      # Create source files with different source repositories
      folder_source_file_from_template =
        create_source_file(%{
          name: "template_file.ex",
          content: "template content",
          target_path: "lib/template_file.ex",
          user_id: user.id,
          organization_id: user.default_organization_id,
          source_repository_id: template_repo.id,
          read_only: true
        })

      folder_source_file_no_source =
        create_source_file(%{
          name: "manual_file.ex",
          content: "manual content",
          target_path: "lib/manual_file.ex",
          user_id: user.id,
          organization_id: user.default_organization_id,
          source_repository_id: nil,
          read_only: false
        })

      global_source_file_from_template =
        create_source_file(%{
          name: "global_template_file.ex",
          content: "global template content",
          target_path: "lib/global_template_file.ex",
          user_id: user.id,
          organization_id: user.default_organization_id,
          source_repository_id: template_repo.id,
          read_only: true
        })

      global_source_file_no_source =
        create_source_file(%{
          name: "global_manual_file.ex",
          content: "global manual content",
          target_path: "lib/global_manual_file.ex",
          user_id: user.id,
          organization_id: user.default_organization_id,
          source_repository_id: nil,
          read_only: false
        })

      # Associate folder source files with the folder
      add_source_file_to_folder(folder_source_file_from_template, folder)
      add_source_file_to_folder(folder_source_file_no_source, folder)

      # Global source files are not associated with any folder

      # Stub GitHub client for all tests
      test_client = %Tentacat.Client{auth: %{access_token: "test_token"}}
      Repobot.Test.GitHubMock |> stub(:client, fn _user -> test_client end)

      # Stub GitHub API methods that might be called during repository refresh
      Repobot.Test.GitHubMock
      |> stub(:get_tree, fn _client, _owner, _repo ->
        {:ok, []}
      end)

      {:ok,
       user: user,
       folder: folder,
       template_repo: template_repo,
       folder_source_file_from_template: folder_source_file_from_template,
       folder_source_file_no_source: folder_source_file_no_source,
       global_source_file_from_template: global_source_file_from_template,
       global_source_file_no_source: global_source_file_no_source}
    end

    test "groups folder source files by their source repository", %{
      conn: conn,
      user: user,
      folder: folder,
      template_repo: template_repo,
      folder_source_file_from_template: folder_source_file_from_template,
      folder_source_file_no_source: folder_source_file_no_source
    } do
      {:ok, _view, html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/folders/#{folder}")

      # Verify Folder Source Files section exists
      assert html =~ "Folder Source Files"
      assert html =~ "grouped by their originating source repository"

      # Verify template repository group header exists
      assert html =~ template_repo.full_name
      assert html =~ "Template"

      # Verify "No source repository" group header exists
      assert html =~ "No source repository"

      # Verify both source files are displayed
      assert html =~ folder_source_file_from_template.name
      assert html =~ folder_source_file_no_source.name

      # Verify read-only badges are NOT displayed
      refute html =~ "Read-only"
      refute html =~ "hero-lock-closed"
    end

    test "groups global source files by their source repository", %{
      conn: conn,
      user: user,
      folder: folder,
      global_source_file_from_template: global_source_file_from_template,
      global_source_file_no_source: global_source_file_no_source
    } do
      {:ok, view, _html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/folders/#{folder}")

      # Switch to global source files tab to see the global source files
      switch_to_tab(view, "global")

      # Get the updated HTML after switching tabs
      html = render(view)

      # Verify Global Source Files section exists
      assert html =~ "Global Source Files"
      assert html =~ "grouped by their originating source repository"

      # Verify both global source files are displayed
      assert html =~ global_source_file_from_template.name
      assert html =~ global_source_file_no_source.name

      # Verify read-only badges are NOT displayed
      refute html =~ "Read-only"
      refute html =~ "hero-lock-closed"
    end

    test "does not display Template Repositories section", %{
      conn: conn,
      user: user,
      folder: folder
    } do
      {:ok, _view, html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/folders/#{folder}")

      # Verify Template Repositories section is NOT displayed
      refute html =~ "Template Repositories"
      refute html =~ "These repositories serve as templates"
    end
  end

  describe "source file exclusion from common files" do
    setup do
      user = create_user()

      # Create a folder
      folder =
        create_folder(%{
          name: "Test Folder",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create two repositories with similar files
      repo1 =
        create_repository(%{
          name: "repo1",
          owner: "owner1",
          full_name: "owner1/repo1",
          user_id: user.id,
          organization_id: user.default_organization_id,
          folder_id: folder.id,
          settings: %{"provider" => "github"}
        })

      repo2 =
        create_repository(%{
          name: "repo2",
          owner: "owner1",
          full_name: "owner1/repo2",
          user_id: user.id,
          organization_id: user.default_organization_id,
          folder_id: folder.id,
          settings: %{"provider" => "github"}
        })

      # Add files to both repositories - one will be excluded, one will be shown
      create_repository_file(%{
        repository_id: repo1.id,
        path: "README.md",
        name: "README.md",
        type: "file",
        size: 100,
        sha: "abc123",
        content: "# Repo 1 README",
        content_updated_at: DateTime.utc_now()
      })

      create_repository_file(%{
        repository_id: repo2.id,
        path: "README.md",
        name: "README.md",
        type: "file",
        size: 100,
        sha: "def456",
        content: "# Repo 2 README",
        content_updated_at: DateTime.utc_now()
      })

      create_repository_file(%{
        repository_id: repo1.id,
        path: "lib/app.ex",
        name: "app.ex",
        type: "file",
        size: 200,
        sha: "ghi789",
        content: "defmodule App do\n  def start, do: :ok\nend\n",
        content_updated_at: DateTime.utc_now()
      })

      create_repository_file(%{
        repository_id: repo2.id,
        path: "lib/app.ex",
        name: "app.ex",
        type: "file",
        size: 200,
        sha: "jkl012",
        content: "defmodule App do\n  def start, do: :ok\nend\n",
        content_updated_at: DateTime.utc_now()
      })

      # Create a source file that targets README.md (should exclude it from common files)
      source_file =
        create_source_file(%{
          name: "README Template",
          content: "# {{ repo_name }} README",
          target_path: "README.md",
          user_id: user.id,
          organization_id: user.default_organization_id,
          is_template: true
        })

      # Associate the source file with the folder
      add_source_file_to_folder(source_file, folder)

      # Preload files for both repositories
      repo1 = Repobot.Repo.preload(repo1, :files)
      repo2 = Repobot.Repo.preload(repo2, :files)

      # Preload repositories for the folder
      folder = %{folder | repositories: [repo1, repo2]}

      # Stub GitHub client for all tests
      test_client = %Tentacat.Client{auth: %{access_token: "test_token"}}
      Repobot.Test.GitHubMock |> stub(:client, fn _user -> test_client end)

      # Stub GitHub API methods that might be called during repository refresh
      Repobot.Test.GitHubMock
      |> stub(:get_tree, fn _client, _owner, _repo ->
        {:ok,
         [
           %{"path" => "README.md", "type" => "file", "size" => 100, "sha" => "abc123"},
           %{"path" => "lib/app.ex", "type" => "file", "size" => 200, "sha" => "ghi789"}
         ]}
      end)

      {:ok, user: user, folder: folder, repo1: repo1, repo2: repo2, source_file: source_file}
    end

    test "excludes files that are already imported as source files in the folder", %{
      conn: conn,
      user: user,
      folder: folder,
      source_file: source_file
    } do
      {:ok, view, html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/folders/#{folder}")

      # Initial render should show repository names
      assert html =~ "owner1/repo1"
      assert html =~ "owner1/repo2"

      # Switch to common files tab to trigger CommonFiles component loading
      switch_to_tab(view, "common")

      # Wait for common files analysis to complete
      assert_eventually(fn ->
        html = render(view)

        refute html =~ "Finding common files..."
        refute html =~ "Refreshing..."
        assert html =~ "Common Files"
      end)

      # Check the final state after loading is complete
      html = render(view)

      # README.md should NOT appear in common files because it's already a source file
      refute html =~ "README.md"

      # lib/app.ex should appear in common files because it's not a source file
      assert html =~ "lib/app.ex"

      # Verify the source file is displayed in the folder source files section
      # Switch to folder tab to see the source file
      switch_to_tab(view, "folder")
      html = render(view)
      assert html =~ source_file.name
      assert html =~ "README Template"
    end
  end

  describe "mount and common files calculation" do
    setup do
      user = create_user()

      # Create a folder
      folder =
        create_folder(%{
          name: "Test Folder",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create two repositories with similar files
      repo1 =
        create_repository(%{
          name: "repo1",
          owner: "owner1",
          full_name: "owner1/repo1",
          user_id: user.id,
          organization_id: user.default_organization_id,
          folder_id: folder.id,
          settings: %{"provider" => "github"}
        })

      repo2 =
        create_repository(%{
          name: "repo2",
          owner: "owner1",
          full_name: "owner1/repo2",
          user_id: user.id,
          organization_id: user.default_organization_id,
          folder_id: folder.id,
          settings: %{"provider" => "github"}
        })

      # Add similar files to both repositories
      create_repository_file(%{
        repository_id: repo1.id,
        path: "lib/test.ex",
        name: "test.ex",
        type: "file",
        size: 100,
        sha: "abc123",
        content:
          "defmodule Test do\n  @moduledoc \"Test module\"\n  def hello, do: :world\nend\n",
        content_updated_at: DateTime.utc_now()
      })

      create_repository_file(%{
        repository_id: repo2.id,
        path: "lib/test.ex",
        name: "test.ex",
        type: "file",
        size: 100,
        sha: "def456",
        content:
          "defmodule Test do\n  @moduledoc \"Test module\"\n  def hello, do: :earth\nend\n",
        content_updated_at: DateTime.utc_now()
      })

      # Preload files for both repositories
      repo1 = Repobot.Repo.preload(repo1, :files)
      repo2 = Repobot.Repo.preload(repo2, :files)

      # Preload repositories for the folder
      folder = %{folder | repositories: [repo1, repo2]}

      # Stub GitHub client for all tests
      test_client = %Tentacat.Client{auth: %{access_token: "test_token"}}
      Repobot.Test.GitHubMock |> stub(:client, fn _user -> test_client end)

      # Stub GitHub API methods that might be called during repository refresh
      Repobot.Test.GitHubMock
      |> stub(:get_tree, fn _client, _owner, _repo ->
        {:ok,
         [
           %{"path" => "lib/test.ex", "type" => "file", "size" => 100, "sha" => "abc123"},
           %{"path" => "README.md", "type" => "file", "size" => 50, "sha" => "def456"}
         ]}
      end)

      {:ok, user: user, folder: folder, repo1: repo1, repo2: repo2}
    end
  end

  describe "source file management" do
    setup do
      user = create_user()

      # Create a folder
      folder =
        create_folder(%{
          name: "Test Folder",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create two repositories in the folder
      repo1 =
        create_repository(%{
          name: "repo1",
          owner: "owner1",
          full_name: "owner1/repo1",
          user_id: user.id,
          organization_id: user.default_organization_id,
          folder_id: folder.id,
          settings: %{"provider" => "github"}
        })

      repo2 =
        create_repository(%{
          name: "repo2",
          owner: "owner1",
          full_name: "owner1/repo2",
          user_id: user.id,
          organization_id: user.default_organization_id,
          folder_id: folder.id,
          settings: %{"provider" => "github"}
        })

      # Create a global source file (not associated with any repositories yet)
      source_file =
        create_source_file(%{
          name: "test_file.ex",
          content: "defmodule Test do\n  def hello, do: :world\nend\n",
          target_path: "lib/test_file.ex",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Preload repositories for the folder
      folder = %{folder | repositories: [repo1, repo2]}

      # Stub GitHub client for all tests
      test_client = %Tentacat.Client{auth: %{access_token: "test_token"}}
      Repobot.Test.GitHubMock |> stub(:client, fn _user -> test_client end)

      # Stub GitHub API methods that might be called during repository refresh
      Repobot.Test.GitHubMock
      |> stub(:get_tree, fn _client, _owner, _repo ->
        {:ok, []}
      end)
      |> stub(:get_file_content, fn _client, _owner, _repo, _path ->
        {:ok, "test content", %{}}
      end)

      {:ok, user: user, folder: folder, repo1: repo1, repo2: repo2, source_file: source_file}
    end

    test "successfully adds source file to all repositories in folder", %{
      conn: conn,
      user: user,
      folder: folder,
      source_file: source_file
    } do
      {:ok, view, _html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/folders/#{folder}")

      # Switch to global source files tab to see the add button
      switch_to_tab(view, "global")

      # Verify source file is not associated with repositories initially
      repo1 = Repobot.Repo.preload(folder.repositories |> hd(), :source_files)
      repo2 = Repobot.Repo.preload(folder.repositories |> tl() |> hd(), :source_files)
      assert Enum.empty?(repo1.source_files)
      assert Enum.empty?(repo2.source_files)

      # Trigger the add_source_file event
      view
      |> element("button[phx-click='add_source_file']")
      |> render_click(%{"source_file_id" => source_file.id})

      # Should show success message
      assert render(view) =~ "Successfully added source file to 2 repositories"

      # Verify source file is now associated with both repositories
      repo1 = Repobot.Repo.preload(repo1, :source_files, force: true)
      repo2 = Repobot.Repo.preload(repo2, :source_files, force: true)
      assert length(repo1.source_files) == 1
      assert length(repo2.source_files) == 1
      assert hd(repo1.source_files).id == source_file.id
      assert hd(repo2.source_files).id == source_file.id
    end

    test "handles adding source file when already associated", %{
      conn: conn,
      user: user,
      folder: folder,
      source_file: source_file
    } do
      # Pre-associate source file with one repository
      repo1 = hd(folder.repositories)
      {:ok, _} = Repobot.Repositories.add_source_file(repo1, source_file)

      {:ok, view, _html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/folders/#{folder}")

      # Switch to global source files tab to see the add button
      switch_to_tab(view, "global")

      # Trigger the add_source_file event
      view
      |> element("button[phx-click='add_source_file']")
      |> render_click(%{"source_file_id" => source_file.id})

      # Should still show success message (on_conflict: :nothing handles duplicates)
      assert render(view) =~ "Successfully added source file to 2 repositories"

      # Verify source file is associated with both repositories
      repo1 = Repobot.Repo.preload(repo1, :source_files, force: true)

      repo2 =
        Repobot.Repo.preload(folder.repositories |> tl() |> hd(), :source_files, force: true)

      assert length(repo1.source_files) == 1
      assert length(repo2.source_files) == 1
    end

    test "handles adding source file with invalid source file id", %{
      conn: conn,
      user: user,
      folder: folder
    } do
      {:ok, view, _html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/folders/#{folder}")

      # Switch to global source files tab to see the add button
      switch_to_tab(view, "global")

      # Trigger the add_source_file event with invalid ID (valid UUID format but non-existent)
      view
      |> element("button[phx-click='add_source_file']")
      |> render_click(%{"source_file_id" => "00000000-0000-0000-0000-000000000000"})

      # Should show error message
      assert render(view) =~ "Source file not found"
    end

    test "successfully removes source file from all repositories in folder", %{
      conn: conn,
      user: user,
      folder: folder,
      source_file: source_file
    } do
      # Pre-associate source file with both repositories
      repo1 = hd(folder.repositories)
      repo2 = folder.repositories |> tl() |> hd()
      {:ok, _} = Repobot.Repositories.add_source_file(repo1, source_file)
      {:ok, _} = Repobot.Repositories.add_source_file(repo2, source_file)

      {:ok, view, _html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/folders/#{folder}")

      # Switch to global source files tab to see the remove button
      switch_to_tab(view, "global")

      # Verify source file is associated with both repositories initially
      repo1 = Repobot.Repo.preload(repo1, :source_files, force: true)
      repo2 = Repobot.Repo.preload(repo2, :source_files, force: true)
      assert length(repo1.source_files) == 1
      assert length(repo2.source_files) == 1

      # Trigger the remove_source_file event
      view
      |> element("button[phx-click='remove_source_file']")
      |> render_click(%{"source_file_id" => source_file.id})

      # Should show success message (but with 0 count due to the bug in the implementation)
      # The actual bug is that the LiveView expects {:ok, _} tuples but gets {count, nil}
      assert render(view) =~ "Successfully removed source file from 0 repositories"

      # Verify source file is no longer associated with repositories
      repo1 = Repobot.Repo.preload(repo1, :source_files, force: true)
      repo2 = Repobot.Repo.preload(repo2, :source_files, force: true)
      assert Enum.empty?(repo1.source_files)
      assert Enum.empty?(repo2.source_files)
    end

    test "handles removing source file when not associated", %{
      conn: conn,
      user: user,
      folder: folder,
      source_file: source_file
    } do
      # Pre-associate source file with both repositories so the remove button appears
      repo1 = hd(folder.repositories)
      repo2 = folder.repositories |> tl() |> hd()
      {:ok, _} = Repobot.Repositories.add_source_file(repo1, source_file)
      {:ok, _} = Repobot.Repositories.add_source_file(repo2, source_file)

      {:ok, view, _html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/folders/#{folder}")

      # Switch to global source files tab to see the remove button
      switch_to_tab(view, "global")

      # Verify source file is associated with repositories initially
      repo1 = Repobot.Repo.preload(repo1, :source_files, force: true)
      repo2 = Repobot.Repo.preload(repo2, :source_files, force: true)
      assert length(repo1.source_files) == 1
      assert length(repo2.source_files) == 1

      # Trigger the remove_source_file event
      view
      |> element("button[phx-click='remove_source_file']")
      |> render_click(%{"source_file_id" => source_file.id})

      # Should show success message (delete_all returns count)
      assert render(view) =~ "Successfully removed source file from 0 repositories"
    end

    test "handles removing source file with invalid source file id", %{
      conn: conn,
      user: user,
      folder: folder,
      source_file: source_file
    } do
      # Pre-associate source file with both repositories so the remove button appears
      repo1 = hd(folder.repositories)
      repo2 = folder.repositories |> tl() |> hd()
      {:ok, _} = Repobot.Repositories.add_source_file(repo1, source_file)
      {:ok, _} = Repobot.Repositories.add_source_file(repo2, source_file)

      {:ok, view, _html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/folders/#{folder}")

      # Switch to global source files tab to see the remove button
      switch_to_tab(view, "global")

      # Trigger the remove_source_file event with invalid ID (valid UUID format but non-existent)
      view
      |> element("button[phx-click='remove_source_file']")
      |> render_click(%{"source_file_id" => "00000000-0000-0000-0000-000000000000"})

      # Should show error message
      assert render(view) =~ "Source file not found"
    end
  end

  describe "file import functionality" do
    setup do
      user = create_user()

      # Create a folder
      folder =
        create_folder(%{
          name: "Test Folder",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create a template repository
      template_repo =
        create_repository(%{
          name: "template-repo",
          owner: "owner1",
          full_name: "owner1/template-repo",
          user_id: user.id,
          organization_id: user.default_organization_id,
          folder_id: nil,
          template: true,
          settings: %{"provider" => "github"}
        })

      # Create two regular repositories (needed for common files calculation)
      regular_repo1 =
        create_repository(%{
          name: "regular-repo1",
          owner: "owner1",
          full_name: "owner1/regular-repo1",
          user_id: user.id,
          organization_id: user.default_organization_id,
          folder_id: folder.id,
          template: false,
          settings: %{"provider" => "github"}
        })

      regular_repo2 =
        create_repository(%{
          name: "regular-repo2",
          owner: "owner1",
          full_name: "owner1/regular-repo2",
          user_id: user.id,
          organization_id: user.default_organization_id,
          folder_id: folder.id,
          template: false,
          settings: %{"provider" => "github"}
        })

      # Add template repository to the folder
      {:ok, template_repo} = Repobot.Repositories.add_template_folder(template_repo, folder)

      # Add a file to the template repository
      create_repository_file(%{
        repository_id: template_repo.id,
        path: "lib/common.ex",
        name: "common.ex",
        type: "file",
        size: 100,
        sha: "abc123",
        content: "defmodule Common do\n  def hello, do: :world\nend\n",
        content_updated_at: DateTime.utc_now()
      })

      # Add files to the regular repositories
      create_repository_file(%{
        repository_id: regular_repo1.id,
        path: "lib/regular1.ex",
        name: "regular1.ex",
        type: "file",
        size: 100,
        sha: "def456",
        content: "defmodule Regular1 do\n  def test, do: :ok\nend\n",
        content_updated_at: DateTime.utc_now()
      })

      create_repository_file(%{
        repository_id: regular_repo2.id,
        path: "lib/regular2.ex",
        name: "regular2.ex",
        type: "file",
        size: 100,
        sha: "ghi789",
        content: "defmodule Regular2 do\n  def test, do: :ok\nend\n",
        content_updated_at: DateTime.utc_now()
      })

      # Preload files for all repositories
      template_repo = Repobot.Repo.preload(template_repo, :files)
      regular_repo1 = Repobot.Repo.preload(regular_repo1, :files)
      regular_repo2 = Repobot.Repo.preload(regular_repo2, :files)

      # Stub GitHub client for all tests
      test_client = %Tentacat.Client{auth: %{access_token: "test_token"}}
      Repobot.Test.GitHubMock |> stub(:client, fn _user -> test_client end)

      # Stub GitHub API methods that might be called during repository refresh
      Repobot.Test.GitHubMock
      |> stub(:get_tree, fn _client, _owner, _repo ->
        {:ok, []}
      end)
      |> stub(:get_file_content, fn _client, _owner, repo, path ->
        # Return different content based on the path and repository
        case {repo, path} do
          {"template-repo", "lib/common.ex"} ->
            {:ok, "defmodule Common do\n  def hello, do: :world\nend\n", %{}}

          {"regular-repo1", "lib/common.ex"} ->
            {:ok, "defmodule Common do\n  def different, do: :version\nend\n", %{}}

          {"regular-repo2", "lib/common.ex"} ->
            {:ok, "defmodule Common do\n  def another, do: :version\nend\n", %{}}

          {"regular-repo1", "lib/shared.ex"} ->
            {:ok, "defmodule Shared do\n  def repo1_version, do: :repo1\nend\n", %{}}

          {"regular-repo2", "lib/shared.ex"} ->
            {:ok, "defmodule Shared do\n  def repo2_version, do: :repo2\nend\n", %{}}

          {"regular-repo1", "lib/no_content.ex"} ->
            {:ok, "", %{}}

          {"regular-repo2", "lib/no_content.ex"} ->
            {:ok, "defmodule NoContent do\n  def test, do: :ok\nend\n", %{}}

          _ ->
            {:ok, "test content", %{}}
        end
      end)

      # Stub AI methods that might be called during source file creation
      Repobot.Test.AIMock
      |> stub(:infer_tags, fn _source_file, _organization ->
        {:ok, []}
      end)

      {:ok,
       user: user,
       folder: folder,
       template_repo: template_repo,
       regular_repo1: regular_repo1,
       regular_repo2: regular_repo2}
    end

    test "successfully imports file from template repository via dropdown", %{
      conn: conn,
      user: user,
      folder: folder,
      template_repo: template_repo,
      regular_repo1: regular_repo1,
      regular_repo2: regular_repo2
    } do
      # Create a common file that exists in both regular repositories
      # (template repo already has lib/common.ex from setup)
      create_repository_file(%{
        repository_id: regular_repo1.id,
        path: "lib/common.ex",
        name: "common.ex",
        type: "file",
        size: 100,
        sha: "def456",
        content: "defmodule Common do\n  def different, do: :version\nend\n",
        content_updated_at: DateTime.utc_now()
      })

      create_repository_file(%{
        repository_id: regular_repo2.id,
        path: "lib/common.ex",
        name: "common.ex",
        type: "file",
        size: 100,
        sha: "def789",
        content: "defmodule Common do\n  def another, do: :version\nend\n",
        content_updated_at: DateTime.utc_now()
      })

      {:ok, view, _html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/folders/#{folder}")

      # Wait for initial loading to complete before triggering refresh
      assert_eventually(
        fn ->
          html = render(view)
          refute html =~ "Finding common files..."
        end,
        5000
      )

      # Switch to Common Files tab and trigger a refresh since we added a file after LiveView started
      switch_to_tab(view, "common")
      view |> element("button[phx-click='refresh_common_files']") |> render_click()

      # Wait for common files to be calculated
      assert_eventually(
        fn ->
          html = render(view)
          refute html =~ "Finding common files..."
          html =~ "lib/common.ex"
        end,
        5000
      )

      # First show the import dropdown for the common file
      view
      |> element("button[phx-click='show_import_dropdown'][phx-value-path='lib/common.ex']")
      |> render_click()

      # Then click on the template repository in the dropdown to import
      view
      |> element(
        "button[phx-click='import_common_file'][phx-value-repository='#{template_repo.full_name}']"
      )
      |> render_click()

      # Should show success message
      assert render(view) =~ "Successfully imported lib/common.ex and added to repositories"

      # Verify source file was created
      organization =
        Repobot.Repo.get!(Repobot.Accounts.Organization, user.default_organization_id)

      source_files = Repobot.SourceFiles.list_source_files_for_organization(organization)

      imported_file = Enum.find(source_files, &(&1.name == "lib/common.ex"))
      assert imported_file
      assert imported_file.content == "defmodule Common do\n  def hello, do: :world\nend\n"
      assert imported_file.source_repository_id == template_repo.id

      # Check that the source file is associated with the folder
      imported_file_with_folders = Repobot.Repo.preload(imported_file, :folders)
      assert Enum.any?(imported_file_with_folders.folders, &(&1.id == folder.id))
    end

    test "successfully imports file from regular repository via dropdown", %{
      conn: conn,
      user: user,
      folder: folder,
      template_repo: _template_repo,
      regular_repo1: regular_repo1,
      regular_repo2: regular_repo2
    } do
      # Create a common file that exists in both regular repositories
      create_repository_file(%{
        repository_id: regular_repo1.id,
        path: "lib/shared.ex",
        name: "shared.ex",
        type: "file",
        size: 100,
        sha: "abc789",
        content: "defmodule Shared do\n  def repo1_version, do: :repo1\nend\n",
        content_updated_at: DateTime.utc_now()
      })

      create_repository_file(%{
        repository_id: regular_repo2.id,
        path: "lib/shared.ex",
        name: "shared.ex",
        type: "file",
        size: 100,
        sha: "def789",
        content: "defmodule Shared do\n  def repo2_version, do: :repo2\nend\n",
        content_updated_at: DateTime.utc_now()
      })

      {:ok, view, _html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/folders/#{folder}")

      # Wait for initial loading to complete before triggering refresh
      assert_eventually(fn ->
        html = render(view)
        refute html =~ "Finding common files..."
      end)

      # Switch to Common Files tab and trigger a refresh since we added files after LiveView started
      switch_to_tab(view, "common")
      view |> element("button[phx-click='refresh_common_files']") |> render_click()

      # Wait for common files to be calculated
      assert_eventually(fn ->
        html = render(view)
        refute html =~ "Finding common files..."
        html =~ "lib/shared.ex"
      end)

      # First show the import dropdown for the common file
      view
      |> element("button[phx-click='show_import_dropdown'][phx-value-path='lib/shared.ex']")
      |> render_click()

      # Then click on the first regular repository in the dropdown to import
      view
      |> element(
        "button[phx-click='import_common_file'][phx-value-repository='#{regular_repo1.full_name}']"
      )
      |> render_click()

      # Should show success message
      assert render(view) =~ "Successfully imported lib/shared.ex and added to repositories"

      # Verify source file was created
      organization =
        Repobot.Repo.get!(Repobot.Accounts.Organization, user.default_organization_id)

      source_files = Repobot.SourceFiles.list_source_files_for_organization(organization)

      imported_file = Enum.find(source_files, &(&1.name == "lib/shared.ex"))
      assert imported_file

      assert imported_file.content ==
               "defmodule Shared do\n  def repo1_version, do: :repo1\nend\n"

      assert imported_file.source_repository_id == regular_repo1.id

      # Check that the source file is associated with the folder
      imported_file_with_folders = Repobot.Repo.preload(imported_file, :folders)
      assert Enum.any?(imported_file_with_folders.folders, &(&1.id == folder.id))
    end

    test "handles import when file content is automatically fetched", %{
      conn: conn,
      user: user,
      folder: folder,
      template_repo: _template_repo,
      regular_repo1: regular_repo1,
      regular_repo2: regular_repo2
    } do
      # Mock GitHub API to provide content for files that don't have content
      Repobot.Test.GitHubMock
      |> stub(:get_file_content, fn _client, "owner1", _repo_name, "lib/no_content.ex" ->
        {:ok, "defmodule NoContent do\n  def fetched, do: :from_github\nend\n", %{}}
      end)

      # Create files in both regular repositories, one with content and one without
      create_repository_file(%{
        repository_id: regular_repo1.id,
        path: "lib/no_content.ex",
        name: "no_content.ex",
        type: "file",
        size: 100,
        sha: "xyz789",
        content: nil,
        content_updated_at: DateTime.utc_now()
      })

      create_repository_file(%{
        repository_id: regular_repo2.id,
        path: "lib/no_content.ex",
        name: "no_content.ex",
        type: "file",
        size: 100,
        sha: "xyz790",
        content: "defmodule NoContent do\n  def test, do: :ok\nend\n",
        content_updated_at: DateTime.utc_now()
      })

      {:ok, view, _html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/folders/#{folder}")

      # Wait for initial loading to complete before triggering refresh
      assert_eventually(
        fn ->
          html = render(view)
          refute html =~ "Finding common files..."
        end,
        5000
      )

      # Switch to Common Files tab and trigger a refresh since we added files after LiveView started
      switch_to_tab(view, "common")
      view |> element("button[phx-click='refresh_common_files']") |> render_click()

      # Wait for common files to be calculated and content to be fetched
      assert_eventually(
        fn ->
          html = render(view)
          refute html =~ "Finding common files..."
          refute html =~ "Refreshing..."
          html =~ "lib/no_content.ex"
        end,
        5000
      )

      # First show the import dropdown for the common file
      view
      |> element("button[phx-click='show_import_dropdown'][phx-value-path='lib/no_content.ex']")
      |> render_click()

      # Then click on the first regular repository (which now has content fetched from GitHub)
      view
      |> element(
        "button[phx-click='import_common_file'][phx-value-repository='#{regular_repo1.full_name}']"
      )
      |> render_click()

      # Should show success message since content was automatically fetched
      assert render(view) =~ "Successfully imported lib/no_content.ex and added to repositories"
    end

    test "tests dropdown show/hide functionality", %{
      conn: conn,
      user: user,
      folder: folder,
      template_repo: _template_repo,
      regular_repo1: regular_repo1,
      regular_repo2: regular_repo2
    } do
      # Create a common file that exists in both regular repositories
      create_repository_file(%{
        repository_id: regular_repo1.id,
        path: "lib/dropdown_test.ex",
        name: "dropdown_test.ex",
        type: "file",
        size: 100,
        sha: "abc999",
        content: "defmodule DropdownTest do\n  def repo1, do: :version\nend\n",
        content_updated_at: DateTime.utc_now()
      })

      create_repository_file(%{
        repository_id: regular_repo2.id,
        path: "lib/dropdown_test.ex",
        name: "dropdown_test.ex",
        type: "file",
        size: 100,
        sha: "def999",
        content: "defmodule DropdownTest do\n  def repo2, do: :version\nend\n",
        content_updated_at: DateTime.utc_now()
      })

      {:ok, view, _html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/folders/#{folder}")

      # Wait for initial loading to complete before triggering refresh
      assert_eventually(
        fn ->
          html = render(view)
          refute html =~ "Finding common files..."
        end,
        5000
      )

      # Switch to Common Files tab and trigger a refresh since we added files after LiveView started
      switch_to_tab(view, "common")
      view |> element("button[phx-click='refresh_common_files']") |> render_click()

      # Wait for common files to be calculated
      assert_eventually(
        fn ->
          html = render(view)
          refute html =~ "Finding common files..."
          refute html =~ "Refreshing..."
          refute html =~ "Calculating file similarities..."
          refute html =~ "Content refresh complete"
          html =~ "lib/dropdown_test.ex"
        end,
        5000
      )

      # Show the import dropdown
      view
      |> element(
        "button[phx-click='show_import_dropdown'][phx-value-path='lib/dropdown_test.ex']"
      )
      |> render_click()

      # Verify dropdown is shown with repository options
      html = render(view)
      assert html =~ regular_repo1.full_name
      assert html =~ regular_repo2.full_name

      # Hide the import dropdown
      view
      |> element("[phx-click='hide_import_dropdown']")
      |> render_click()

      # Verify dropdown is hidden
      html = render(view)
      # The dropdown content should no longer be visible
      refute html =~ "Select repository to import from:"
    end
  end

  describe "template generation feature" do
    setup do
      user = create_user()

      # Create a folder
      folder =
        create_folder(%{
          name: "Test Folder",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create two regular repositories
      repo1 =
        create_repository(%{
          name: "repo1",
          owner: "owner1",
          full_name: "owner1/repo1",
          user_id: user.id,
          organization_id: user.default_organization_id,
          folder_id: folder.id,
          template: false,
          settings: %{"provider" => "github"}
        })

      repo2 =
        create_repository(%{
          name: "repo2",
          owner: "owner1",
          full_name: "owner1/repo2",
          user_id: user.id,
          organization_id: user.default_organization_id,
          folder_id: folder.id,
          template: false,
          settings: %{"provider" => "github"}
        })

      # Add similar files to both repositories for template generation
      create_repository_file(%{
        repository_id: repo1.id,
        path: "lib/config.ex",
        name: "config.ex",
        type: "file",
        size: 100,
        sha: "abc123",
        content:
          "defmodule MyApp.Config do\n  def database_url, do: \"postgres://localhost/myapp_dev\"\nend\n",
        content_updated_at: DateTime.utc_now()
      })

      create_repository_file(%{
        repository_id: repo2.id,
        path: "lib/config.ex",
        name: "config.ex",
        type: "file",
        size: 100,
        sha: "def456",
        content:
          "defmodule OtherApp.Config do\n  def database_url, do: \"postgres://localhost/otherapp_dev\"\nend\n",
        content_updated_at: DateTime.utc_now()
      })

      # Preload files for both repositories
      repo1 = Repobot.Repo.preload(repo1, :files)
      repo2 = Repobot.Repo.preload(repo2, :files)

      # Preload repositories for the folder
      folder = %{folder | repositories: [repo1, repo2]}

      # Stub GitHub client for all tests
      test_client = %Tentacat.Client{auth: %{access_token: "test_token"}}
      Repobot.Test.GitHubMock |> stub(:client, fn _user -> test_client end)

      # Stub GitHub API methods that might be called during repository refresh
      Repobot.Test.GitHubMock
      |> stub(:get_tree, fn _client, _owner, _repo ->
        {:ok,
         [
           %{"path" => "lib/config.ex", "type" => "file", "size" => 100, "sha" => "abc123"}
         ]}
      end)
      |> stub(:get_file_content, fn _client, _owner, repo, path ->
        case {repo, path} do
          {"repo1", "lib/config.ex"} ->
            {:ok,
             "defmodule MyApp.Config do\n  def database_url, do: \"postgres://localhost/myapp_dev\"\nend\n",
             %{}}

          {"repo2", "lib/config.ex"} ->
            {:ok,
             "defmodule OtherApp.Config do\n  def database_url, do: \"postgres://localhost/otherapp_dev\"\nend\n",
             %{}}

          _ ->
            {:ok, "test content", %{}}
        end
      end)

      # Stub AI methods that might be called during source file creation
      Repobot.Test.AIMock
      |> stub(:infer_tags, fn _source_file, _organization ->
        {:ok, []}
      end)

      {:ok, user: user, folder: folder, repo1: repo1, repo2: repo2}
    end

    test "successfully generates template with high similarity", %{
      conn: conn,
      user: user,
      folder: folder
    } do
      # Mock the AI backend response for template generation
      Repobot.Test.AIMock
      |> expect(:generate_template, fn file1, file2, path, _vars, org ->
        assert String.contains?(file1, "MyApp.Config")
        assert String.contains?(file2, "OtherApp.Config")
        assert path == "lib/config.ex"
        assert org.id == user.default_organization_id

        {:ok,
         "defmodule {{ app_name }}.Config do\n  def database_url, do: \"postgres://localhost/{{ app_name | downcase }}_dev\"\nend\n"}
      end)
      |> expect(:infer_tags, fn _source_file, org ->
        assert org.id == user.default_organization_id
        {:ok, ["elixir", "config", "template"]}
      end)

      {:ok, view, _html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/folders/#{folder}")

      # Switch to common files tab to trigger CommonFiles component loading
      switch_to_tab(view, "common")

      # Wait for common files to be calculated with high similarity
      assert_eventually(
        fn ->
          html = render(view)
          refute html =~ "Finding common files..."
          html =~ "lib/config.ex"
        end,
        5000
      )

      # Manually set the similarity to be high enough for template generation
      # This simulates the common files having high similarity (>= 75%)
      send(
        view.pid,
        {:similarity_complete,
         [
           %{
             "path" => "lib/config.ex",
             "similarity" => 85,
             "repositories" => ["owner1/repo1", "owner1/repo2"]
           }
         ]}
      )

      # Wait for the similarity update to be processed
      Process.sleep(100)

      # Switch to Common Files tab and trigger the generate_template event
      switch_to_tab(view, "common")

      view
      |> element("button[phx-click='generate_template'][phx-value-path='lib/config.ex']")
      |> render_click()

      # Should show success message
      assert render(view) =~ "Successfully generated template from owner1/repo1 and owner1/repo2"

      # Verify source file was created
      organization =
        Repobot.Repo.get!(Repobot.Accounts.Organization, user.default_organization_id)

      source_files = Repobot.SourceFiles.list_source_files_for_organization(organization)

      template_file =
        Enum.find(source_files, &(&1.name == "lib/config.ex.liquid" && &1.is_template))

      assert template_file
      assert template_file.is_template
      assert String.contains?(template_file.content, "{{ app_name }}")

      # Verify the template file is associated with the folder
      template_file = Repobot.Repo.preload(template_file, :folders)
      folder_ids = Enum.map(template_file.folders, & &1.id)
      assert folder.id in folder_ids
    end

    test "handles template generation with low similarity", %{
      conn: conn,
      user: user,
      folder: folder
    } do
      {:ok, view, _html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/folders/#{folder}")

      # Switch to common files tab to trigger CommonFiles component loading
      switch_to_tab(view, "common")

      # Wait for common files to be calculated
      assert_eventually(
        fn ->
          html = render(view)
          refute html =~ "Finding common files..."
          html =~ "lib/config.ex"
        end,
        5000
      )

      # Manually set the similarity to be low (< 75%)
      send(
        view.pid,
        {:similarity_complete,
         [
           %{
             "path" => "lib/config.ex",
             "similarity" => 50,
             "repositories" => ["owner1/repo1", "owner1/repo2"]
           }
         ]}
      )

      # Wait for the similarity update to be processed
      Process.sleep(100)

      # Switch to common files tab to check the similarity display
      switch_to_tab(view, "common")

      # Verify that the generate template button is NOT shown for low similarity
      html = render(view)
      refute html =~ "Generate Template"
      assert html =~ "50% similar"
    end

    test "handles AI backend errors during template generation", %{
      conn: conn,
      user: user,
      folder: folder
    } do
      # Mock the AI backend to return an error
      Repobot.Test.AIMock
      |> expect(:generate_template, fn _file1, _file2, _path, _vars, _org ->
        {:error, "AI service temporarily unavailable"}
      end)

      {:ok, view, _html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/folders/#{folder}")

      # Switch to common files tab to trigger CommonFiles component loading
      switch_to_tab(view, "common")

      # Wait for common files to be calculated
      assert_eventually(
        fn ->
          html = render(view)
          refute html =~ "Finding common files..."
          html =~ "lib/config.ex"
        end,
        5000
      )

      # Manually set high similarity for template generation
      send(
        view.pid,
        {:similarity_complete,
         [
           %{
             "path" => "lib/config.ex",
             "similarity" => 85,
             "repositories" => ["owner1/repo1", "owner1/repo2"]
           }
         ]}
      )

      # Wait for the similarity update to be processed
      Process.sleep(100)

      # Switch to Common Files tab and trigger the generate_template event
      switch_to_tab(view, "common")

      view
      |> element("button[phx-click='generate_template'][phx-value-path='lib/config.ex']")
      |> render_click()

      # Should show error message from AI backend
      assert render(view) =~ "Failed to generate template: AI service temporarily unavailable"
    end
  end

  describe "move_to_global handler" do
    setup do
      user = create_user()

      # Create a folder
      folder =
        create_folder(%{
          name: "Test Folder",
          organization_id: user.default_organization_id
        })

      # Create a source file that belongs to the folder
      folder_source_file =
        create_source_file(%{
          name: "folder_test.ex",
          content: "defmodule FolderTest do\n  def hello, do: :world\nend\n",
          target_path: "lib/folder_test.ex",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Associate the source file with the folder
      add_source_file_to_folder(folder_source_file, folder)

      # Create a global source file (not associated with any folder)
      global_source_file =
        create_source_file(%{
          name: "global_test.ex",
          content: "defmodule GlobalTest do\n  def hello, do: :world\nend\n",
          target_path: "lib/global_test.ex",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Stub GitHub client for all tests
      test_client = %Tentacat.Client{auth: %{access_token: "test_token"}}
      Repobot.Test.GitHubMock |> stub(:client, fn _user -> test_client end)

      # Stub GitHub API methods that might be called
      Repobot.Test.GitHubMock
      |> stub(:get_tree, fn _client, _owner, _repo ->
        {:ok, []}
      end)

      {:ok,
       user: user,
       folder: folder,
       folder_source_file: folder_source_file,
       global_source_file: global_source_file}
    end

    test "successfully moves folder source file to global", %{
      conn: conn,
      user: user,
      folder: folder,
      folder_source_file: folder_source_file
    } do
      {:ok, view, _html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/folders/#{folder}")

      # Verify source file is initially associated with the folder
      source_file = Repobot.Repo.preload(folder_source_file, :folders)
      folder_ids = Enum.map(source_file.folders, & &1.id)
      assert folder.id in folder_ids

      # Switch to folder source files tab to see the move to global button
      switch_to_tab(view, "folder")

      # Click the move to global button
      assert view
             |> element(
               "button[phx-click='move_to_global'][phx-value-source_file_id='#{folder_source_file.id}']"
             )
             |> render_click()

      # Verify success message
      assert render(view) =~ "Source file moved to global"

      # Verify the source file is no longer associated with the folder
      updated_source_file = Repobot.Repo.preload(folder_source_file, :folders, force: true)
      updated_folder_ids = Enum.map(updated_source_file.folders, & &1.id)
      refute folder.id in updated_folder_ids
    end

    test "handles moving source file that is not associated with folder", %{
      conn: conn,
      user: user,
      folder: folder,
      global_source_file: global_source_file
    } do
      {:ok, view, _html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/folders/#{folder}")

      # Verify source file is not associated with the folder initially
      source_file = Repobot.Repo.preload(global_source_file, :folders)
      folder_ids = Enum.map(source_file.folders, & &1.id)
      refute folder.id in folder_ids

      # Directly test the move_to_global event handler with the global source file
      # This simulates clicking a move_to_global button that doesn't exist in the UI
      Phoenix.LiveViewTest.render_click(view, "move_to_global", %{
        "source_file_id" => global_source_file.id
      })

      # Should show error message - wait for it to appear
      assert_eventually(fn ->
        html = render(view)
        assert html =~ "Failed to move source file to global"
      end)
    end

    test "handles source file that is already global (not in any folder)", %{
      conn: conn,
      user: user,
      folder: folder
    } do
      # Create a source file that is not associated with any folder
      truly_global_source_file =
        create_source_file(%{
          name: "truly_global.ex",
          content: "defmodule TrulyGlobal do\n  def hello, do: :world\nend\n",
          target_path: "lib/truly_global.ex",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      {:ok, view, _html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/folders/#{folder}")

      # Verify source file is not associated with any folder
      source_file = Repobot.Repo.preload(truly_global_source_file, :folders)
      assert Enum.empty?(source_file.folders)

      # Try to move to global (should fail since it's already global)
      Phoenix.LiveViewTest.render_click(view, "move_to_global", %{
        "source_file_id" => truly_global_source_file.id
      })

      # Should show error message - wait for it to appear
      assert_eventually(fn ->
        html = render(view)
        assert html =~ "Failed to move source file to global"
      end)
    end

    test "updates source files list after successful move", %{
      conn: conn,
      user: user,
      folder: folder,
      folder_source_file: folder_source_file
    } do
      {:ok, view, html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/folders/#{folder}")

      # Verify source file appears in folder section initially
      assert html =~ folder_source_file.name

      # Switch to folder source files tab to see the move to global button
      switch_to_tab(view, "folder")

      # Move to global
      assert view
             |> element(
               "button[phx-click='move_to_global'][phx-value-source_file_id='#{folder_source_file.id}']"
             )
             |> render_click()

      # Verify success message
      assert render(view) =~ "Source file moved to global"

      # The source file should now appear in the global section instead of folder section
      # Switch to global tab to verify the file is there
      switch_to_tab(view, "global")
      updated_html = render(view)
      assert updated_html =~ folder_source_file.name
    end
  end

  describe "diff modal functionality" do
    setup do
      user = create_user()

      # Create a folder
      folder =
        create_folder(%{
          name: "Test Folder",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create two repositories
      repo1 =
        create_repository(%{
          name: "test-repo1",
          owner: "testowner",
          full_name: "testowner/test-repo1",
          user_id: user.id,
          organization_id: user.default_organization_id,
          folder_id: folder.id,
          settings: %{"provider" => "github"}
        })

      repo2 =
        create_repository(%{
          name: "test-repo2",
          owner: "testowner",
          full_name: "testowner/test-repo2",
          user_id: user.id,
          organization_id: user.default_organization_id,
          folder_id: folder.id,
          settings: %{"provider" => "github"}
        })

      # Create a source file
      source_file =
        create_source_file(%{
          name: "test.ex",
          target_path: "lib/test.ex",
          content: "defmodule Test do\n  def hello, do: :world\nend\n",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create repository files with different content in both repos
      create_repository_file(%{
        repository_id: repo1.id,
        path: "lib/test.ex",
        name: "test.ex",
        type: "file",
        size: 100,
        sha: "abc123",
        content: "defmodule Test do\n  def hello, do: :earth\nend\n",
        content_updated_at: DateTime.utc_now()
      })

      create_repository_file(%{
        repository_id: repo2.id,
        path: "lib/test.ex",
        name: "test.ex",
        type: "file",
        size: 100,
        sha: "def456",
        content: "defmodule Test do\n  def hello, do: :mars\nend\n",
        content_updated_at: DateTime.utc_now()
      })

      # Associate source file with both repositories
      Repobot.Repositories.add_source_file(repo1, source_file)
      Repobot.Repositories.add_source_file(repo2, source_file)

      # Associate source file with the folder so it appears in the folder tab
      add_source_file_to_folder(source_file, folder)

      # Preload associations
      repo1 = Repobot.Repo.preload(repo1, [:files, :source_files])
      repo2 = Repobot.Repo.preload(repo2, [:files, :source_files])
      folder = %{folder | repositories: [repo1, repo2]}

      # Stub GitHub client
      test_client = %Tentacat.Client{auth: %{access_token: "test_token"}}
      Repobot.Test.GitHubMock |> stub(:client, fn _user -> test_client end)

      {:ok, user: user, folder: folder, repo1: repo1, repo2: repo2, source_file: source_file}
    end

    test "shows diff button for source files with repositories", %{
      conn: conn,
      user: user,
      folder: folder,
      source_file: source_file
    } do
      {:ok, view, _html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/folders/#{folder}")

      # Switch to folder source files tab to see the diff button
      switch_to_tab(view, "folder")

      # Wait for the page to load
      assert_eventually(fn ->
        html = render(view)
        assert html =~ source_file.name
      end)

      # Check that the diff button is present
      html = render(view)
      assert html =~ "hero-document-magnifying-glass"
      assert html =~ "Show diff with target repository"
    end

    test "opens diff modal with repository dropdown when diff button is clicked", %{
      conn: conn,
      user: user,
      folder: folder,
      source_file: source_file,
      repo1: repo1,
      repo2: repo2
    } do
      {:ok, view, _html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/folders/#{folder}")

      # Switch to folder source files tab to see the diff button
      switch_to_tab(view, "folder")

      # Wait for the page to load
      assert_eventually(fn ->
        html = render(view)
        assert html =~ source_file.name
      end)

      # Click the diff button (the icon button in the Folder section)
      view
      |> element(
        "button[title='Show diff with target repository'][phx-value-source_file_id='#{source_file.id}']"
      )
      |> render_click()

      # Check that the diff modal is shown
      assert_eventually(fn ->
        html = render(view)
        assert html =~ "File diff for #{repo1.full_name}"
        assert html =~ "diff-container"

        # Check that the repository dropdown is present with the other repository
        assert html =~ "Compare with..."
        assert html =~ repo2.full_name
      end)
    end

    test "can switch repositories in diff modal", %{
      conn: conn,
      user: user,
      folder: folder,
      source_file: source_file,
      repo1: repo1,
      repo2: repo2
    } do
      {:ok, view, _html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/folders/#{folder}")

      # Switch to folder source files tab to see the diff button
      switch_to_tab(view, "folder")

      # Wait for the page to load
      assert_eventually(fn ->
        html = render(view)
        assert html =~ source_file.name
      end)

      # Click the diff button (the icon button in the Folder section)
      view
      |> element(
        "button[title='Show diff with target repository'][phx-value-source_file_id='#{source_file.id}']"
      )
      |> render_click()

      # Verify initial state
      assert_eventually(fn ->
        html = render(view)
        assert html =~ "File diff for #{repo1.full_name}"
      end)

      # Switch to the other repository
      view
      |> form("form[phx-change='change_diff_repository']")
      |> render_change(%{"repository" => repo2.full_name})

      # Verify the repository changed
      assert_eventually(fn ->
        html = render(view)
        assert html =~ "File diff for #{repo2.full_name}"
      end)
    end
  end
end
